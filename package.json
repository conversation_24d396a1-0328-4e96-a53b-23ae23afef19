{"name": "omnispace", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "setup:appwrite": "tsx src/scripts/setup-appwrite.ts", "seed:appwrite": "tsx src/scripts/seed-appwrite.ts", "diagnose:appwrite": "tsx src/scripts/diagnose-appwrite.ts", "test:user-profile": "tsx src/scripts/test-user-profile.ts", "setup:admin-env": "tsx src/scripts/setup-admin-environment.ts", "test:admin-login": "tsx src/scripts/test-admin-login.ts", "verify:admin-setup": "tsx src/scripts/verify-admin-setup.ts", "debug:admin-user": "tsx src/scripts/debug-admin-user.ts", "test:auth-components": "tsx src/scripts/test-auth-components.ts"}, "dependencies": {"@ai-sdk/google": "1.2.22", "@ai-sdk/mistral": "1.2.8", "@ai-sdk/openai": "2.0.0-beta.14", "@ai-sdk/react": "2.0.0-beta.31", "@hookform/resolvers": "^5.2.0", "@monaco-editor/react": "^4.7.0", "@novnc/novnc": "^1.6.0", "@prisma/client": "^6.13.0", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-context-menu": "^2.2.15", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "^1.1.15", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@types/dockerode": "^3.3.42", "@types/pg": "^8.15.5", "@types/ssh2": "^1.15.5", "ai": "5.0.0-beta.31", "appwrite": "^18.2.0", "bcryptjs": "^3.0.2", "better-auth": "^1.3.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "dockerode": "^4.0.7", "embla-carousel-react": "^8.6.0", "framer-motion": "^12.23.11", "immer": "^10.1.1", "input-otp": "^1.4.2", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.533.0", "monaco-editor": "^0.52.2", "next": "15.4.4", "next-themes": "^0.4.6", "nextjs-toploader": "^3.8.16", "node-appwrite": "17.0.0", "pg": "^8.16.3", "prisma": "^6.13.0", "react": "19.1.0", "react-day-picker": "^9.8.1", "react-dom": "19.1.0", "react-dropzone": "^14.3.8", "react-hook-form": "^7.61.1", "react-resizable-panels": "^3.0.3", "recharts": "2.15.4", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "sonner": "^2.0.6", "ssh2": "^1.16.0", "tailwind-merge": "^3.3.1", "vaul": "^1.1.2", "ws": "^8.18.3", "zod": "^4.0.13", "zustand": "^5.0.6"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/jsonwebtoken": "^9.0.10", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/ws": "^8.18.1", "eslint": "^9", "eslint-config-next": "15.4.4", "tailwindcss": "^4", "tsx": "^4.20.3", "tw-animate-css": "^1.3.6", "typescript": "^5"}}