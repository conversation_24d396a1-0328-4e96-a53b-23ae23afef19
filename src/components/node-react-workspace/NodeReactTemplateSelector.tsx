'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { 
  Search, 
  Code, 
  Globe, 
  Zap, 
  Server, 
  Layers,
  ExternalLink,
  Clock,
  Users,
  Package,
  Cpu,
  Database
} from 'lucide-react';
import { NodeProjectTemplate, NodeFramework } from '@/types/node-react-workspace';
import { nodeReactWorkspaceService } from '@/services/node-react-workspace';

interface NodeReactTemplateSelectorProps {
  onTemplateSelect: (template: NodeProjectTemplate) => void;
  selectedTemplate?: NodeProjectTemplate;
  className?: string;
}

const frameworkIcons: Record<NodeFramework, React.ReactNode> = {
  nextjs: <Zap className="h-5 w-5" />,
  react: <Code className="h-5 w-5" />,
  express: <Server className="h-5 w-5" />,
  nestjs: <Layers className="h-5 w-5" />,
  vue: <Globe className="h-5 w-5" />,
  angular: <Package className="h-5 w-5" />,
  nuxt: <Zap className="h-5 w-5" />,
  svelte: <Code className="h-5 w-5" />,
  remix: <Globe className="h-5 w-5" />,
  gatsby: <Globe className="h-5 w-5" />,
};

const frameworkColors: Record<NodeFramework, string> = {
  nextjs: 'bg-black text-white',
  react: 'bg-blue-500 text-white',
  express: 'bg-green-600 text-white',
  nestjs: 'bg-red-500 text-white',
  vue: 'bg-green-500 text-white',
  angular: 'bg-red-600 text-white',
  nuxt: 'bg-green-400 text-white',
  svelte: 'bg-orange-500 text-white',
  remix: 'bg-blue-600 text-white',
  gatsby: 'bg-purple-600 text-white',
};

export function NodeReactTemplateSelector({ 
  onTemplateSelect, 
  selectedTemplate,
  className = '' 
}: NodeReactTemplateSelectorProps) {
  const [templates, setTemplates] = useState<NodeProjectTemplate[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedFramework, setSelectedFramework] = useState<string>('all');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadTemplates();
  }, []);

  const loadTemplates = async () => {
    try {
      setLoading(true);
      setError(null);
      const fetchedTemplates = await nodeReactWorkspaceService.getTemplates();
      setTemplates(fetchedTemplates);
    } catch (err) {
      console.error('Error loading templates:', err);
      setError('Failed to load templates');
    } finally {
      setLoading(false);
    }
  };

  const filteredTemplates = templates.filter(template => {
    const matchesSearch = template.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         template.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         template.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));
    
    const matchesFramework = selectedFramework === 'all' || template.framework === selectedFramework;
    
    const matchesCategory = selectedCategory === 'all' || 
                           (selectedCategory === 'frontend' && ['react', 'vue', 'angular', 'svelte'].includes(template.framework)) ||
                           (selectedCategory === 'fullstack' && ['nextjs', 'nuxt', 'remix', 'gatsby'].includes(template.framework)) ||
                           (selectedCategory === 'backend' && ['express', 'nestjs'].includes(template.framework));
    
    return matchesSearch && matchesFramework && matchesCategory;
  });

  const frameworks = Array.from(new Set(templates.map(t => t.framework)));
  const categories = ['frontend', 'fullstack', 'backend'];

  if (loading) {
    return (
      <div className={`space-y-4 ${className}`}>
        <div className="text-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="mt-2 text-muted-foreground">Loading templates...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`space-y-4 ${className}`}>
        <div className="text-center py-8">
          <p className="text-red-500">{error}</p>
          <Button onClick={loadTemplates} className="mt-2">
            Retry
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      <div className="text-center">
        <h3 className="text-2xl font-bold mb-2">Choose a Node/React Template</h3>
        <p className="text-muted-foreground">
          Select from our curated collection of modern JavaScript and TypeScript frameworks
        </p>
      </div>

      {/* Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search templates..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
        <Select value={selectedCategory} onValueChange={setSelectedCategory}>
          <SelectTrigger className="w-full sm:w-48">
            <SelectValue placeholder="All Categories" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Categories</SelectItem>
            {categories.map(category => (
              <SelectItem key={category} value={category}>
                <div className="flex items-center gap-2">
                  {category === 'frontend' && <Code className="h-4 w-4" />}
                  {category === 'fullstack' && <Globe className="h-4 w-4" />}
                  {category === 'backend' && <Server className="h-4 w-4" />}
                  {category.charAt(0).toUpperCase() + category.slice(1)}
                </div>
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        <Select value={selectedFramework} onValueChange={setSelectedFramework}>
          <SelectTrigger className="w-full sm:w-48">
            <SelectValue placeholder="All Frameworks" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Frameworks</SelectItem>
            {frameworks.map(framework => (
              <SelectItem key={framework} value={framework}>
                <div className="flex items-center gap-2">
                  {frameworkIcons[framework]}
                  {framework.charAt(0).toUpperCase() + framework.slice(1)}
                </div>
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Templates Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <AnimatePresence>
          {filteredTemplates.map((template) => (
            <motion.div
              key={template.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.2 }}
            >
              <Card 
                className={`cursor-pointer transition-all duration-200 hover:shadow-lg ${
                  selectedTemplate?.id === template.id 
                    ? 'ring-2 ring-primary border-primary' 
                    : 'hover:border-primary/50'
                }`}
                onClick={() => onTemplateSelect(template)}
              >
                <CardHeader className="pb-3">
                  <div className="flex items-start justify-between">
                    <div className="flex items-center gap-2">
                      <span className="text-2xl">{template.icon}</span>
                      <div>
                        <CardTitle className="text-lg">{template.name}</CardTitle>
                        <Badge className={`mt-1 ${frameworkColors[template.framework]}`}>
                          {template.framework}
                        </Badge>
                      </div>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  <CardDescription className="text-sm">
                    {template.description}
                  </CardDescription>
                  
                  <div className="flex flex-wrap gap-1">
                    {template.tags.slice(0, 3).map(tag => (
                      <Badge key={tag} variant="secondary" className="text-xs">
                        {tag}
                      </Badge>
                    ))}
                    {template.tags.length > 3 && (
                      <Badge variant="secondary" className="text-xs">
                        +{template.tags.length - 3}
                      </Badge>
                    )}
                  </div>
                  
                  <div className="flex items-center justify-between text-sm text-muted-foreground">
                    <div className="flex items-center gap-1">
                      <Database className="h-3 w-3" />
                      <span>Port {template.defaultPort}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Package className="h-3 w-3" />
                      <span>{template.dependencies.length} deps</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </AnimatePresence>
      </div>

      {filteredTemplates.length === 0 && (
        <div className="text-center py-8">
          <p className="text-muted-foreground">No templates found matching your criteria</p>
        </div>
      )}

      {/* Selected template info */}
      {selectedTemplate && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mt-6"
        >
          <Card className="border-primary/20 bg-primary/5">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <span>Selected: {selectedTemplate.name}</span>
                <Badge className={frameworkColors[selectedTemplate.framework]}>
                  {selectedTemplate.framework}
                </Badge>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between">
                <p className="text-sm text-muted-foreground">
                  Ready to create your {selectedTemplate.framework} project
                </p>
                <Button variant="outline" size="sm" asChild>
                  <a 
                    href={selectedTemplate.documentation} 
                    target="_blank" 
                    rel="noopener noreferrer"
                    className="flex items-center gap-1"
                  >
                    <ExternalLink className="h-3 w-3" />
                    Docs
                  </a>
                </Button>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      )}
    </div>
  );
}
